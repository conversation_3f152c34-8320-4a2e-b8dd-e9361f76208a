'use client'

import { Chessboard } from 'react-chessboard'
import { Square } from 'chess.js'
import { useChessGame } from '@/hooks/useChessGame'
import { Crown, RotateCcw, Flag } from 'lucide-react'
import { useMemo } from 'react'

interface ChessBoardProps {
  gameId?: string
  playerColor?: 'white' | 'black'
  isSpectator?: boolean
  onMove?: (move: any) => void
  initialFen?: string
}

export default function ChessBoard({
  playerColor = 'white',
  isSpectator = false,
  onMove,
  initialFen
}: ChessBoardProps) {
  const {
    gameState,
    selectedSquare,
    possibleMoves,
    onSquareClick,
    onPieceDrop,
    resetGame,
    resignGame,
    isCheck,
    isCheckmate,
    isDraw,
    isGameOver,
    currentFen
  } = useChessGame(initialFen)

  // Custom square styles for highlighting
  const customSquareStyles = useMemo(() => {
    const styles: { [square: string]: React.CSSProperties } = {}

    // Highlight selected square
    if (selectedSquare) {
      styles[selectedSquare] = {
        backgroundColor: 'rgba(255, 255, 0, 0.4)'
      }
    }

    // Highlight possible moves
    possibleMoves.forEach(square => {
      styles[square] = {
        background: 'radial-gradient(circle, rgba(0,0,0,.1) 25%, transparent 25%)',
        borderRadius: '50%'
      }
    })

    // Highlight check
    if (isCheck) {
      const kingSquare = gameState.game.board().flat().find(
        piece => piece && piece.type === 'k' && piece.color === gameState.game.turn()
      )
      if (kingSquare) {
        // Find king position - this is a simplified approach
        for (let rank = 0; rank < 8; rank++) {
          for (let file = 0; file < 8; file++) {
            const square = String.fromCharCode(97 + file) + (8 - rank) as Square
            const piece = gameState.game.get(square)
            if (piece && piece.type === 'k' && piece.color === gameState.game.turn()) {
              styles[square] = {
                backgroundColor: 'rgba(255, 0, 0, 0.4)'
              }
            }
          }
        }
      }
    }

    return styles
  }, [selectedSquare, possibleMoves, isCheck, gameState.game])

  const handlePieceDrop = ({ sourceSquare, targetSquare }: any) => {
    if (isSpectator) return false

    const result = onPieceDrop(sourceSquare as Square, targetSquare as Square)

    if (result && onMove) {
      // Get the last move from the game
      const history = gameState.game.history({ verbose: true })
      const lastMove = history[history.length - 1]
      onMove(lastMove)
    }

    return result
  }

  const handleSquareClick = ({ square }: any) => {
    if (!isSpectator) {
      onSquareClick(square as Square)
    }
  }

  const getGameStatusMessage = () => {
    // Check for resignation first
    if (gameState.resignedBy) {
      const winner = gameState.resignedBy === 'white' ? 'Black' : 'White'
      return `${gameState.resignedBy === 'white' ? 'White' : 'Black'} resigned. ${winner} wins!`
    }
    if (isCheckmate) {
      const winner = gameState.game.turn() === 'w' ? 'Black' : 'White'
      return `Checkmate! ${winner} wins!`
    }
    if (isDraw) {
      return 'Game ended in a draw'
    }
    if (isCheck) {
      return 'Check!'
    }
    if (gameState.gameStatus === 'waiting') {
      return 'Waiting for opponent...'
    }
    return `${gameState.game.turn() === 'w' ? 'White' : 'Black'} to move`
  }

  return (
    <div className="flex flex-col items-center space-y-6 w-full max-w-lg mx-auto">
      {/* Game Status */}
      <div className="bg-white rounded-xl shadow-lg p-5 w-full text-center border border-gray-200">
        <div className="flex items-center justify-center space-x-3 mb-3">
          <Crown className="h-6 w-6 text-yellow-500" />
          <span className="font-bold text-gray-800 text-lg">
            {getGameStatusMessage()}
          </span>
        </div>

        {gameState.gameStatus === 'active' && (
          <div className="text-sm text-gray-600 bg-gray-50 rounded-lg py-2 px-3">
            Current Turn: <span className="font-semibold">{gameState.game.turn() === 'w' ? 'White' : 'Black'}</span>
          </div>
        )}
      </div>

      {/* Chess Board */}
      <div className="relative w-full">
        <Chessboard
          options={{
            position: currentFen,
            onPieceDrop: handlePieceDrop,
            onSquareClick: !isSpectator ? handleSquareClick : undefined,
            boardOrientation: playerColor,
            squareStyles: customSquareStyles,
            allowDrawingArrows: true,
            boardStyle: {
              borderRadius: '12px',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1)',
              width: '100%',
              maxWidth: '480px',
              aspectRatio: '1'
            }
          }}
        />
        
        {/* Game Over Overlay */}
        {isGameOver && (
          <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center rounded-xl backdrop-blur-sm">
            <div className="bg-white p-8 rounded-xl text-center shadow-2xl border border-gray-200 max-w-sm mx-4">
              <div className="mb-4">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Crown className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Game Over</h3>
                <p className="text-gray-600 text-lg">{getGameStatusMessage()}</p>
              </div>
              {!isSpectator && (
                <button
                  onClick={resetGame}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-semibold"
                >
                  <RotateCcw className="h-5 w-5 mr-2" />
                  New Game
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Game Controls */}
      {!isSpectator && gameState.gameStatus === 'active' && (
        <div className="flex flex-col sm:flex-row gap-3 w-full">
          <button
            onClick={resetGame}
            className="flex-1 inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-semibold rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 shadow-sm"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset Game
          </button>

          <button
            onClick={resignGame}
            className="flex-1 inline-flex items-center justify-center px-4 py-3 border border-red-300 text-sm font-semibold rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors duration-200 shadow-sm"
          >
            <Flag className="h-4 w-4 mr-2" />
            Resign
          </button>
        </div>
      )}

      {/* Move History */}
      <div className="bg-white rounded-xl shadow-lg p-6 w-full border border-gray-200">
        <div className="flex items-center mb-4">
          <div className="bg-blue-100 text-blue-600 rounded-lg p-2 mr-3">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-800">Move History</h3>
        </div>
        <div className="max-h-40 overflow-y-auto">
          {gameState.moveHistory.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {gameState.moveHistory.map((move, index) => (
                <div key={index} className="flex items-center p-2 rounded-lg bg-gray-50 text-gray-700 font-mono text-sm">
                  <span className="text-gray-500 mr-2 font-semibold">
                    {Math.floor(index / 2) + 1}.{index % 2 === 0 ? '' : '..'}
                  </span>
                  <span className="font-semibold">{move}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-gray-500 font-medium">No moves yet</p>
              <p className="text-gray-400 text-sm mt-1">Make your first move to start!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
