{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "32b9325f1b67fe6cd65864e7893fd09b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2546b53ee9a69bc0c1b55c19ba08e2ca426781965415371c00ec8cc8a0d9d2f3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "59fd071455f98e28c13b6e5573f3d2f3cb274a7493a9ab06a1d77dd9280fc6ab"}}}, "sortedMiddleware": ["/"], "functions": {}}